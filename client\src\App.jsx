import React, { useState } from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import axios from 'axios';

function App() {
  const [username, setUsername] = useState('admin');
  const [password, setPassword] = useState('admin123');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [user, setUser] = useState(null);

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      const response = await axios.post('http://localhost:5000/api/auth/login', {
        username,
        password
      });

      setUser(response.data.user);
      setMessage('تم تسجيل الدخول بنجاح! / Connexion réussie!');
    } catch (error) {
      setMessage('خطأ في تسجيل الدخول / Erreur de connexion: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    setUser(null);
    setMessage('');
  };

  if (user) {
    return (
      <div className="App">
        <div className="container mt-5">
          <div className="row justify-content-center">
            <div className="col-md-8">
              <div className="card shadow">
                <div className="card-body text-center">
                  <h1 className="card-title text-success mb-4">
                    🎉 مرحباً {user.username}!
                  </h1>
                  <h2 className="card-subtitle text-muted mb-4">
                    Bienvenue {user.username}!
                  </h2>
                  <div className="alert alert-success">
                    <strong>تم تسجيل الدخول بنجاح!</strong><br/>
                    <strong>Connexion réussie!</strong>
                  </div>
                  <div className="mt-4">
                    <p><strong>الدور / Rôle:</strong> {user.role}</p>
                    <p><strong>البريد الإلكتروني / Email:</strong> {user.email}</p>
                    <p><strong>اللغة المفضلة / Langue:</strong> {user.preferred_language}</p>
                  </div>
                  <button className="btn btn-danger" onClick={handleLogout}>
                    تسجيل الخروج / Déconnexion
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="App">
      <div className="container mt-5">
        <div className="row justify-content-center">
          <div className="col-md-6">
            <div className="card shadow">
              <div className="card-body">
                <h1 className="card-title text-primary text-center mb-4">
                  🏢 نظام إدارة الموارد البشرية
                </h1>
                <h2 className="card-subtitle text-muted text-center mb-4">
                  Système de Gestion RH
                </h2>

                <form onSubmit={handleLogin}>
                  <div className="mb-3">
                    <label className="form-label">اسم المستخدم / Nom d'utilisateur</label>
                    <input
                      type="text"
                      className="form-control"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      required
                    />
                  </div>

                  <div className="mb-3">
                    <label className="form-label">كلمة المرور / Mot de passe</label>
                    <input
                      type="password"
                      className="form-control"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                    />
                  </div>

                  <button
                    type="submit"
                    className="btn btn-primary w-100"
                    disabled={loading}
                  >
                    {loading ? 'جاري التحميل... / Chargement...' : 'تسجيل الدخول / Connexion'}
                  </button>
                </form>

                {message && (
                  <div className={`alert mt-3 ${message.includes('نجاح') || message.includes('réussie') ? 'alert-success' : 'alert-danger'}`}>
                    {message}
                  </div>
                )}

                <div className="mt-4 text-center">
                  <small className="text-muted">
                    <strong>بيانات تجريبية / Données de test:</strong><br/>
                    admin / admin123<br/>
                    hr_manager / hr123
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;

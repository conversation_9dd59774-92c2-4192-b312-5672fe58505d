<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام - Test System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .test-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="test-card p-5 text-center">
                    <h1 class="display-4 text-primary mb-4">
                        🎉 النظام يعمل!
                    </h1>
                    <h2 class="h3 text-muted mb-4">
                        Le système fonctionne!
                    </h2>
                    
                    <div class="row text-center mb-4">
                        <div class="col-md-6">
                            <div class="p-3 bg-success bg-opacity-10 rounded">
                                <h5 class="text-success">✅ Frontend</h5>
                                <p class="mb-0">Vite + React</p>
                                <small class="text-muted">Port 3000</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="p-3 bg-info bg-opacity-10 rounded">
                                <h5 class="text-info">🔗 Backend</h5>
                                <p class="mb-0">Node.js + Express</p>
                                <small class="text-muted">Port 5000</small>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-success">
                        <strong>تم تشغيل النظام بنجاح!</strong><br>
                        <strong>Système démarré avec succès!</strong>
                    </div>

                    <div class="mt-4">
                        <button class="btn btn-primary btn-lg me-2" onclick="testAPI()">
                            اختبار API / Test API
                        </button>
                        <button class="btn btn-outline-secondary btn-lg" onclick="goToApp()">
                            الذهاب للتطبيق / Aller à l'app
                        </button>
                    </div>

                    <div id="apiResult" class="mt-4"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<div class="spinner-border text-primary" role="status"></div>';
            
            try {
                const response = await fetch('http://localhost:5000/api/health');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <h6>✅ API يعمل بنجاح / API fonctionne!</h6>
                        <small>Status: ${data.status}</small><br>
                        <small>Message: ${data.message}</small>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6>❌ خطأ في الاتصال / Erreur de connexion</h6>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        function goToApp() {
            window.location.href = '/';
        }
    </script>
</body>
</html>

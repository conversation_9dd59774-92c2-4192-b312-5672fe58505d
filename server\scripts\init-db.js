const { sequelize } = require('../config/database');
const bcrypt = require('bcryptjs');

// Import all models
require('../models');

const initializeDatabase = async () => {
  try {
    console.log('🔄 Initializing database...');
    
    // Force sync to create fresh tables
    await sequelize.sync({ force: true });
    console.log('✅ Database tables created successfully.');

    // Create default users
    const User = require('../models/User');
    
    const adminPassword = await bcrypt.hash('password123', 10);
    const employeePassword = await bcrypt.hash('password123', 10);

    await User.bulkCreate([
      {
        id: '550e8400-e29b-41d4-a716-446655440000',
        username: 'admin',
        email: '<EMAIL>',
        password: adminPassword,
        role: 'admin',
        is_active: true,
        preferred_language: 'ar'
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440001',
        username: 'employee',
        email: '<EMAIL>',
        password: employeePassword,
        role: 'employee',
        is_active: true,
        preferred_language: 'ar'
      }
    ]);

    console.log('✅ Default users created successfully.');
    console.log('📊 Database initialization completed!');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    process.exit(1);
  }
};

initializeDatabase();

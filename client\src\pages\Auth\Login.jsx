import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, But<PERSON>, Al<PERSON>, Spinner } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';

const Login = () => {
  const { t } = useTranslation();
  const { language, changeLanguage } = useLanguage();
  const { login, loading, error, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const [formData, setFormData] = useState({
    username: '',
    password: '',
    rememberMe: false
  });

  const from = location.state?.from?.pathname || '/';

  useEffect(() => {
    if (isAuthenticated) {
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const result = await login(formData);
    if (result.success) {
      navigate(from, { replace: true });
    }
  };

  const handleLanguageToggle = () => {
    const newLang = language === 'ar' ? 'fr' : 'ar';
    changeLanguage(newLang);
  };

  return (
    <div className="min-vh-100 d-flex align-items-center bg-light">
      <Container>
        <Row className="justify-content-center">
          <Col md={6} lg={4}>
            <Card className="shadow-sm border-0">
              <Card.Body className="p-5">
                {/* Header */}
                <div className="text-center mb-4">
                  <div className="mb-3">
                    <i className="bi bi-building text-primary" style={{ fontSize: '3rem' }}></i>
                  </div>
                  <h3 className="fw-bold text-dark mb-2">
                    {language === 'ar' ? 'نظام إدارة الموارد البشرية' : 'Système de Gestion RH'}
                  </h3>
                  <p className="text-muted">
                    {t('auth.login')}
                  </p>
                </div>

                {/* Language Toggle */}
                <div className="text-center mb-4">
                  <Button
                    variant="outline-secondary"
                    size="sm"
                    onClick={handleLanguageToggle}
                  >
                    <i className="bi bi-globe me-2"></i>
                    {language === 'ar' ? 'Français' : 'العربية'}
                  </Button>
                </div>

                {/* Error Alert */}
                {error && (
                  <Alert variant="danger" className="mb-4">
                    <i className="bi bi-exclamation-triangle me-2"></i>
                    {error}
                  </Alert>
                )}

                {/* Login Form */}
                <Form onSubmit={handleSubmit}>
                  <Form.Group className="mb-3">
                    <Form.Label>
                      <i className="bi bi-person me-2"></i>
                      {t('auth.username')}
                    </Form.Label>
                    <Form.Control
                      type="text"
                      name="username"
                      value={formData.username}
                      onChange={handleChange}
                      placeholder={t('auth.username')}
                      required
                      disabled={loading}
                      className="py-2"
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>
                      <i className="bi bi-lock me-2"></i>
                      {t('auth.password')}
                    </Form.Label>
                    <Form.Control
                      type="password"
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      placeholder={t('auth.password')}
                      required
                      disabled={loading}
                      className="py-2"
                    />
                  </Form.Group>

                  <Form.Group className="mb-4">
                    <Form.Check
                      type="checkbox"
                      name="rememberMe"
                      checked={formData.rememberMe}
                      onChange={handleChange}
                      label={t('auth.rememberMe')}
                      disabled={loading}
                    />
                  </Form.Group>

                  <Button
                    type="submit"
                    variant="primary"
                    size="lg"
                    className="w-100 py-2"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                          className="me-2"
                        />
                        {t('common.loading')}
                      </>
                    ) : (
                      <>
                        <i className="bi bi-box-arrow-in-right me-2"></i>
                        {t('auth.login')}
                      </>
                    )}
                  </Button>
                </Form>

                {/* Demo Credentials */}
                <div className="mt-4 p-3 bg-light rounded">
                  <small className="text-muted">
                    <strong>بيانات تجريبية / Données de démonstration:</strong><br />
                    <strong>المدير / Admin:</strong><br />
                    Email: <EMAIL><br />
                    Password: password123<br />
                    <strong>الموظف / Employé:</strong><br />
                    Email: <EMAIL><br />
                    Password: password123
                  </small>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default Login;

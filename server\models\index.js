const { sequelize } = require('../config/database');

// Import all models
const User = require('./User');
const Employee = require('./Employee');
const Department = require('./Department');
const Absence = require('./Absence');
const Leave = require('./Leave');
const Disciplinary = require('./Disciplinary');
const Shift = require('./Shift');
const Notification = require('./Notification');

// Define associations
// User associations
User.hasOne(Employee, {
  foreignKey: 'user_id',
  as: 'employee'
});

Employee.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// Department associations
Department.belongsTo(Department, {
  foreignKey: 'parent_department_id',
  as: 'parentDepartment'
});

Department.hasMany(Department, {
  foreignKey: 'parent_department_id',
  as: 'subDepartments'
});

Department.belongsTo(Employee, {
  foreignKey: 'manager_id',
  as: 'manager'
});

Department.hasMany(Employee, {
  foreignKey: 'department_id',
  as: 'employees'
});

// Employee associations
Employee.belongsTo(Department, {
  foreignKey: 'department_id',
  as: 'department'
});

Employee.hasMany(Absence, {
  foreignKey: 'employee_id',
  as: 'absences'
});

Employee.hasMany(Leave, {
  foreignKey: 'employee_id',
  as: 'leaves'
});

Employee.hasMany(Disciplinary, {
  foreignKey: 'employee_id',
  as: 'disciplinaryActions'
});

Employee.hasMany(Shift, {
  foreignKey: 'employee_id',
  as: 'shifts'
});

// Absence associations
Absence.belongsTo(Employee, {
  foreignKey: 'employee_id',
  as: 'employee'
});

Absence.belongsTo(User, {
  foreignKey: 'approved_by',
  as: 'approver'
});

// Leave associations
Leave.belongsTo(Employee, {
  foreignKey: 'employee_id',
  as: 'employee'
});

Leave.belongsTo(User, {
  foreignKey: 'approved_by',
  as: 'approver'
});

// Disciplinary associations
Disciplinary.belongsTo(Employee, {
  foreignKey: 'employee_id',
  as: 'employee'
});

Disciplinary.belongsTo(User, {
  foreignKey: 'issued_by',
  as: 'issuer'
});

// Shift associations
Shift.belongsTo(Employee, {
  foreignKey: 'employee_id',
  as: 'employee'
});

// Notification associations
Notification.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

User.hasMany(Notification, {
  foreignKey: 'user_id',
  as: 'notifications'
});

module.exports = {
  sequelize,
  User,
  Employee,
  Department,
  Absence,
  Leave,
  Disciplinary,
  Shift,
  Notification
};

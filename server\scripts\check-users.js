const { sequelize } = require('../config/database');
const bcrypt = require('bcryptjs');

// Import all models
require('../models');

const checkUsers = async () => {
  try {
    console.log('🔄 Checking users in database...');
    
    const User = require('../models/User');
    
    const users = await User.findAll();
    console.log(`📊 Found ${users.length} users:`);
    
    for (const user of users) {
      console.log(`\n👤 User: ${user.username}`);
      console.log(`📧 Email: ${user.email}`);
      console.log(`🔑 Role: ${user.role}`);
      console.log(`✅ Active: ${user.is_active}`);
      console.log(`🌐 Language: ${user.preferred_language}`);
      
      // Test password
      const testPassword = 'password123';
      const isMatch = await user.comparePassword(testPassword);
      console.log(`🔐 Password test (${testPassword}): ${isMatch ? '✅ MATCH' : '❌ NO MATCH'}`);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error checking users:', error);
    process.exit(1);
  }
};

checkUsers();
